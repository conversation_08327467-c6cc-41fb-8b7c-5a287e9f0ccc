# 数据中台系统文档目录

## 文档结构说明

本文档按照软件工程标准进行分类和编号，采用层次化的编号体系：

- **PRD** - 产品需求文档 (Product Requirements Document)
- **FRS** - 功能规格说明书 (Functional Requirements Specification)

## 文档编号规则

- PRD-XX：产品需求文档编号
- FRS-XX：功能规格说明书编号
- BPD-XX：业务流程图文档编号
- 其中XX为两位数字，按模块顺序编号

## 目录编号规则

- 01-prd：产品需求文档目录
- 02-frs：功能规格说明书目录
- 03-bpd：业务流程图文档目录

## 模块编号规则

- M01：数据采集模块
- M02：数据治理模块
- M03：数据可视化模块
- M04：数据API管理模块

## 文档清单

### 项目概览
- [项目概览](./项目概览.md) - 数据中台系统整体介绍和架构说明

### 01-产品需求文档 (PRD)
- [PRD-01 数据中台系统总体产品需求文档](./01-prd/PRD-01-数据中台系统总体产品需求文档.md)
- [PRD-02 M01数据采集模块产品需求文档](./01-prd/PRD-02-M01数据采集模块产品需求文档.md)
- [PRD-03 M02数据治理模块产品需求文档](./01-prd/PRD-03-M02数据治理模块产品需求文档.md)
- [PRD-04 M03数据可视化模块产品需求文档](./01-prd/PRD-04-M03数据可视化模块产品需求文档.md)

### 02-功能规格说明书 (FRS)
- [FRS-01 数据中台系统总体功能规格说明书](./02-frs/FRS-01-数据中台系统总体功能规格说明书.md)
- [FRS-02 M01数据采集模块功能规格说明书](./02-frs/FRS-02-M01数据采集模块功能规格说明书.md)
- [FRS-03 M02数据治理模块功能规格说明书](./02-frs/FRS-03-M02数据治理模块功能规格说明书.md)
- [FRS-04 M03数据可视化模块功能规格说明书](./02-frs/FRS-04-M03数据可视化模块功能规格说明书.md)

### 03-业务流程图 (BPD)
- [BPD-01 数据中台系统总体业务流程图](./03-bpd/BPD-01-数据中台系统总体业务流程图.md)

## 架构设计

### C4模型架构图
所有产品需求文档都包含了完整的C4模型架构图：

- **Level 1 - Context（系统上下文图）**：展示系统与外部用户和系统的交互关系
- **Level 2 - Container（容器图）**：展示系统内部的高层技术构建块
- **Level 3 - Component（组件图）**：展示容器内部的组件及其职责
- **Level 4 - Code（代码图）**：在功能规格说明书中体现具体的类和接口设计

### 架构图特点
- 使用Mermaid C4语法绘制，支持在线渲染
- 清晰展示模块间的依赖关系和数据流向
- 包含详细的技术栈和通信协议说明
- 便于开发团队理解系统架构和实施方案

## 版本控制

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0  | 2025-06-27 | 初始版本创建 | 系统分析师 |

## 文档使用说明

1. 产品需求文档(PRD)主要描述产品的业务需求、用户需求和产品目标
2. 功能规格说明书(FRS)详细描述系统的功能需求、技术规格和实现细节
3. 所有文档均采用Markdown格式编写，便于版本控制和协作
4. 业务流程图采用Mermaid语法绘制，支持在线渲染和编辑
