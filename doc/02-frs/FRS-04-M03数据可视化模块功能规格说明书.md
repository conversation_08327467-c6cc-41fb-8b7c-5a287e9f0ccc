# FRS-04 M03数据可视化模块功能规格说明书

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | FRS-04 |
| 文档名称 | M03数据可视化模块功能规格说明书 |
| 版本号 | 1.0 |
| 创建日期 | 2025-06-27 |
| 最后修改日期 | 2025-06-27 |
| 文档状态 | 草稿 |

## 1. 模块架构

### 1.1 技术架构图

```mermaid
graph TB
    subgraph "前端展示层"
        A1[React组件库]
        A2[ECharts图表库]
        A3[AntD UI组件]
        A4[移动端组件]
    end
    
    subgraph "可视化服务层"
        B1[报表设计服务]
        B2[图表渲染服务]
        B3[仪表板服务]
        B4[指标计算服务]
        B5[导出服务]
    end
    
    subgraph "数据处理层"
        C1[数据查询引擎]
        C2[指标计算引擎]
        C3[数据缓存引擎]
        C4[实时数据引擎]
    end
    
    subgraph "存储层"
        D1[报表配置库]
        D2[指标定义库]
        D3[用户偏好库]
        D4[缓存数据库]
        D5[文件存储]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C2
    B5 --> C1
    
    C1 --> D1
    C2 --> D2
    C3 --> D4
    C4 --> D4
    
    B1 --> D1
    B2 --> D1
    B3 --> D3
    B5 --> D5
```

### 1.2 核心组件

#### 1.2.1 报表设计器组件
- **拖拽设计器**：支持组件拖拽和布局设计
- **属性配置器**：配置组件属性和样式
- **数据绑定器**：绑定数据源和字段映射
- **预览器**：实时预览报表效果

#### 1.2.2 图表渲染引擎
- **图表工厂**：创建各种类型的图表实例
- **数据适配器**：适配不同格式的数据源
- **主题管理器**：管理图表主题和样式
- **交互处理器**：处理图表交互事件

#### 1.2.3 指标计算引擎
- **指标解析器**：解析指标定义和计算公式
- **计算调度器**：调度指标计算任务
- **缓存管理器**：管理计算结果缓存
- **依赖分析器**：分析指标依赖关系

## 2. 功能详细设计

### 2.1 报表设计功能

#### 2.1.1 报表模板设计
```java
@Entity
@Table(name = "report_template")
public class ReportTemplate {
    @Id
    private String id;
    private String name;
    private String description;
    private String category;
    private String layout; // JSON格式的布局配置
    private String components; // JSON格式的组件配置
    private String dataSource;
    private String status;
    private String creatorId;
    private Date createTime;
    private Date updateTime;
    
    // getters and setters
}

@Entity
@Table(name = "report_component")
public class ReportComponent {
    @Id
    private String id;
    private String templateId;
    private String componentType; // CHART, TABLE, TEXT, FILTER
    private String componentConfig; // JSON格式的组件配置
    private String dataConfig; // JSON格式的数据配置
    private String styleConfig; // JSON格式的样式配置
    private Integer positionX;
    private Integer positionY;
    private Integer width;
    private Integer height;
    
    // getters and setters
}
```

#### 2.1.2 报表设计服务
```java
@Service
public class ReportDesignService {
    
    @Autowired
    private ReportTemplateRepository templateRepository;
    
    @Autowired
    private ReportComponentRepository componentRepository;
    
    public ReportTemplate createTemplate(ReportTemplateDTO dto) {
        ReportTemplate template = new ReportTemplate();
        BeanUtils.copyProperties(dto, template);
        template.setId(UUID.randomUUID().toString());
        template.setStatus("DRAFT");
        template.setCreateTime(new Date());
        
        return templateRepository.save(template);
    }
    
    public ReportComponent addComponent(String templateId, ComponentDTO dto) {
        ReportComponent component = new ReportComponent();
        component.setId(UUID.randomUUID().toString());
        component.setTemplateId(templateId);
        component.setComponentType(dto.getType());
        component.setComponentConfig(JsonUtils.toJson(dto.getConfig()));
        component.setDataConfig(JsonUtils.toJson(dto.getDataConfig()));
        component.setStyleConfig(JsonUtils.toJson(dto.getStyleConfig()));
        component.setPositionX(dto.getX());
        component.setPositionY(dto.getY());
        component.setWidth(dto.getWidth());
        component.setHeight(dto.getHeight());
        
        return componentRepository.save(component);
    }
    
    public ReportPreviewResult previewReport(String templateId, Map<String, Object> parameters) {
        ReportTemplate template = templateRepository.findById(templateId);
        List<ReportComponent> components = componentRepository.findByTemplateId(templateId);
        
        ReportPreviewResult result = new ReportPreviewResult();
        result.setTemplate(template);
        
        List<ComponentRenderResult> componentResults = new ArrayList<>();
        for (ReportComponent component : components) {
            ComponentRenderResult componentResult = renderComponent(component, parameters);
            componentResults.add(componentResult);
        }
        
        result.setComponents(componentResults);
        return result;
    }
}
```

### 2.2 指标建模功能

#### 2.2.1 指标定义模型
```java
@Entity
@Table(name = "metric_definition")
public class MetricDefinition {
    @Id
    private String id;
    private String name;
    private String displayName;
    private String description;
    private String category;
    private String metricType; // ATOMIC, DERIVED, COMPOSITE
    private String formula; // 计算公式
    private String dimensions; // JSON格式的维度定义
    private String dataSource;
    private String aggregationType; // SUM, COUNT, AVG, MAX, MIN
    private String unit;
    private String status;
    private Date createTime;
    
    // getters and setters
}

@Entity
@Table(name = "metric_calculation_log")
public class MetricCalculationLog {
    @Id
    private String id;
    private String metricId;
    private String calculationType; // REALTIME, BATCH, MANUAL
    private Date startTime;
    private Date endTime;
    private String status; // RUNNING, SUCCESS, FAILED
    private String errorMessage;
    private Integer processedRecords;
    
    // getters and setters
}
```

#### 2.2.2 指标计算引擎
```java
@Component
public class MetricCalculationEngine {
    
    @Autowired
    private MetricDefinitionRepository metricRepository;
    
    @Autowired
    private DataQueryService dataQueryService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public MetricResult calculateMetric(String metricId, Map<String, Object> parameters) {
        MetricDefinition metric = metricRepository.findById(metricId);
        
        // 检查缓存
        String cacheKey = buildCacheKey(metricId, parameters);
        MetricResult cachedResult = (MetricResult) redisTemplate.opsForValue().get(cacheKey);
        if (cachedResult != null && !isExpired(cachedResult)) {
            return cachedResult;
        }
        
        // 计算指标
        MetricResult result = new MetricResult();
        result.setMetricId(metricId);
        result.setCalculateTime(new Date());
        
        switch (metric.getMetricType()) {
            case "ATOMIC":
                result = calculateAtomicMetric(metric, parameters);
                break;
            case "DERIVED":
                result = calculateDerivedMetric(metric, parameters);
                break;
            case "COMPOSITE":
                result = calculateCompositeMetric(metric, parameters);
                break;
        }
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, result, Duration.ofMinutes(30));
        
        return result;
    }
    
    private MetricResult calculateAtomicMetric(MetricDefinition metric, Map<String, Object> parameters) {
        // 原子指标计算：直接从数据源聚合计算
        String sql = buildMetricSQL(metric, parameters);
        List<Map<String, Object>> data = dataQueryService.executeQuery(sql);
        
        MetricResult result = new MetricResult();
        result.setMetricId(metric.getId());
        result.setValue(extractValue(data, metric.getAggregationType()));
        result.setDimensions(extractDimensions(data, metric.getDimensions()));
        
        return result;
    }
    
    private MetricResult calculateDerivedMetric(MetricDefinition metric, Map<String, Object> parameters) {
        // 派生指标计算：基于其他指标计算
        FormulaParser parser = new FormulaParser();
        Formula formula = parser.parse(metric.getFormula());
        
        Map<String, MetricResult> dependentMetrics = new HashMap<>();
        for (String dependentMetricId : formula.getDependentMetrics()) {
            MetricResult dependentResult = calculateMetric(dependentMetricId, parameters);
            dependentMetrics.put(dependentMetricId, dependentResult);
        }
        
        MetricResult result = new MetricResult();
        result.setMetricId(metric.getId());
        result.setValue(formula.calculate(dependentMetrics));
        
        return result;
    }
}
```

### 2.3 仪表板功能

#### 2.3.1 仪表板配置
```java
@Entity
@Table(name = "dashboard")
public class Dashboard {
    @Id
    private String id;
    private String name;
    private String description;
    private String layout; // JSON格式的布局配置
    private String theme;
    private String refreshInterval;
    private String accessLevel; // PUBLIC, PRIVATE, SHARED
    private String ownerId;
    private String status;
    private Date createTime;
    private Date updateTime;
    
    // getters and setters
}

@Entity
@Table(name = "dashboard_widget")
public class DashboardWidget {
    @Id
    private String id;
    private String dashboardId;
    private String widgetType; // CHART, METRIC_CARD, TABLE, TEXT
    private String title;
    private String dataSource;
    private String queryConfig; // JSON格式的查询配置
    private String chartConfig; // JSON格式的图表配置
    private String styleConfig; // JSON格式的样式配置
    private Integer positionX;
    private Integer positionY;
    private Integer width;
    private Integer height;
    
    // getters and setters
}
```

#### 2.3.2 仪表板服务
```java
@Service
public class DashboardService {
    
    @Autowired
    private DashboardRepository dashboardRepository;
    
    @Autowired
    private DashboardWidgetRepository widgetRepository;
    
    @Autowired
    private DataQueryService dataQueryService;
    
    public Dashboard createDashboard(DashboardDTO dto) {
        Dashboard dashboard = new Dashboard();
        BeanUtils.copyProperties(dto, dashboard);
        dashboard.setId(UUID.randomUUID().toString());
        dashboard.setStatus("ACTIVE");
        dashboard.setCreateTime(new Date());
        
        return dashboardRepository.save(dashboard);
    }
    
    public DashboardWidget addWidget(String dashboardId, WidgetDTO dto) {
        DashboardWidget widget = new DashboardWidget();
        widget.setId(UUID.randomUUID().toString());
        widget.setDashboardId(dashboardId);
        widget.setWidgetType(dto.getType());
        widget.setTitle(dto.getTitle());
        widget.setDataSource(dto.getDataSource());
        widget.setQueryConfig(JsonUtils.toJson(dto.getQueryConfig()));
        widget.setChartConfig(JsonUtils.toJson(dto.getChartConfig()));
        widget.setStyleConfig(JsonUtils.toJson(dto.getStyleConfig()));
        widget.setPositionX(dto.getX());
        widget.setPositionY(dto.getY());
        widget.setWidth(dto.getWidth());
        widget.setHeight(dto.getHeight());
        
        return widgetRepository.save(widget);
    }
    
    public DashboardData loadDashboardData(String dashboardId, Map<String, Object> filters) {
        Dashboard dashboard = dashboardRepository.findById(dashboardId);
        List<DashboardWidget> widgets = widgetRepository.findByDashboardId(dashboardId);
        
        DashboardData data = new DashboardData();
        data.setDashboard(dashboard);
        
        List<WidgetData> widgetDataList = new ArrayList<>();
        for (DashboardWidget widget : widgets) {
            WidgetData widgetData = loadWidgetData(widget, filters);
            widgetDataList.add(widgetData);
        }
        
        data.setWidgets(widgetDataList);
        return data;
    }
    
    private WidgetData loadWidgetData(DashboardWidget widget, Map<String, Object> filters) {
        QueryConfig queryConfig = JsonUtils.fromJson(widget.getQueryConfig(), QueryConfig.class);
        
        // 应用过滤条件
        applyFilters(queryConfig, filters);
        
        // 执行查询
        List<Map<String, Object>> rawData = dataQueryService.executeQuery(queryConfig);
        
        // 转换数据格式
        WidgetData widgetData = new WidgetData();
        widgetData.setWidgetId(widget.getId());
        widgetData.setData(transformDataForWidget(rawData, widget.getWidgetType()));
        
        return widgetData;
    }
}
```

## 3. 前端组件设计

### 3.1 React组件架构

```typescript
// 报表设计器主组件
interface ReportDesignerProps {
  templateId?: string;
  onSave: (template: ReportTemplate) => void;
  onPreview: (template: ReportTemplate) => void;
}

const ReportDesigner: React.FC<ReportDesignerProps> = ({ templateId, onSave, onPreview }) => {
  const [template, setTemplate] = useState<ReportTemplate>();
  const [components, setComponents] = useState<ReportComponent[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<string>();

  return (
    <div className="report-designer">
      <DesignerToolbar onSave={handleSave} onPreview={handlePreview} />
      <div className="designer-content">
        <ComponentPalette onDragStart={handleDragStart} />
        <DesignCanvas
          components={components}
          selectedComponent={selectedComponent}
          onComponentSelect={setSelectedComponent}
          onComponentUpdate={handleComponentUpdate}
        />
        <PropertyPanel
          component={selectedComponent}
          onPropertyChange={handlePropertyChange}
        />
      </div>
    </div>
  );
};

// 图表组件
interface ChartComponentProps {
  type: ChartType;
  data: any[];
  config: ChartConfig;
  style: ChartStyle;
  onInteraction?: (event: ChartEvent) => void;
}

const ChartComponent: React.FC<ChartComponentProps> = ({ type, data, config, style, onInteraction }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chart, setChart] = useState<EChartsInstance>();

  useEffect(() => {
    if (chartRef.current) {
      const chartInstance = echarts.init(chartRef.current);
      setChart(chartInstance);

      return () => {
        chartInstance.dispose();
      };
    }
  }, []);

  useEffect(() => {
    if (chart && data) {
      const option = buildChartOption(type, data, config, style);
      chart.setOption(option);

      if (onInteraction) {
        chart.on('click', onInteraction);
      }
    }
  }, [chart, data, config, style]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};
```

### 3.2 图表配置接口

```typescript
interface ChartConfig {
  title?: {
    text: string;
    position: 'top' | 'bottom' | 'left' | 'right';
    style: TextStyle;
  };
  legend?: {
    show: boolean;
    position: 'top' | 'bottom' | 'left' | 'right';
    orient: 'horizontal' | 'vertical';
  };
  xAxis?: {
    type: 'category' | 'value' | 'time';
    name: string;
    data?: string[];
  };
  yAxis?: {
    type: 'category' | 'value';
    name: string;
    min?: number;
    max?: number;
  };
  series: SeriesConfig[];
  tooltip?: {
    show: boolean;
    trigger: 'item' | 'axis';
    formatter?: string;
  };
  dataZoom?: {
    show: boolean;
    type: 'slider' | 'inside';
  };
}

interface SeriesConfig {
  name: string;
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap';
  data: any[];
  color?: string;
  stack?: string;
  smooth?: boolean;
}
```

## 4. API接口设计

### 4.1 报表管理API

```yaml
paths:
  /api/v1/reports/templates:
    get:
      summary: 获取报表模板列表
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: keyword
          in: query
          schema:
            type: string
      responses:
        200:
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ReportTemplate'

    post:
      summary: 创建报表模板
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportTemplateDTO'
      responses:
        201:
          description: 创建成功

  /api/v1/reports/templates/{id}/preview:
    post:
      summary: 预览报表
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                parameters:
                  type: object
      responses:
        200:
          description: 预览结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPreviewResult'

  /api/v1/reports/templates/{id}/export:
    post:
      summary: 导出报表
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: format
          in: query
          schema:
            type: string
            enum: [pdf, excel, image]
      responses:
        200:
          description: 导出文件
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
```

### 4.2 仪表板API

```yaml
paths:
  /api/v1/dashboards:
    get:
      summary: 获取仪表板列表
      responses:
        200:
          description: 成功

    post:
      summary: 创建仪表板
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DashboardDTO'
      responses:
        201:
          description: 创建成功

  /api/v1/dashboards/{id}/data:
    get:
      summary: 获取仪表板数据
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: filters
          in: query
          schema:
            type: object
      responses:
        200:
          description: 仪表板数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardData'

  /api/v1/dashboards/{id}/widgets:
    post:
      summary: 添加仪表板组件
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WidgetDTO'
      responses:
        201:
          description: 添加成功
```

### 4.3 指标管理API

```yaml
paths:
  /api/v1/metrics:
    get:
      summary: 获取指标列表
      parameters:
        - name: category
          in: query
          schema:
            type: string
      responses:
        200:
          description: 成功

    post:
      summary: 创建指标
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MetricDefinitionDTO'
      responses:
        201:
          description: 创建成功

  /api/v1/metrics/{id}/calculate:
    post:
      summary: 计算指标值
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                parameters:
                  type: object
                dimensions:
                  type: array
                  items:
                    type: string
      responses:
        200:
          description: 计算结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MetricResult'
```

## 5. 数据库设计

### 5.1 核心表结构

```sql
-- 报表模板表
CREATE TABLE report_template (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    layout TEXT,
    components TEXT,
    data_source VARCHAR(100),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    creator_id VARCHAR(32),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 报表组件表
CREATE TABLE report_component (
    id VARCHAR(32) PRIMARY KEY,
    template_id VARCHAR(32),
    component_type VARCHAR(50) NOT NULL,
    component_config TEXT,
    data_config TEXT,
    style_config TEXT,
    position_x INT,
    position_y INT,
    width INT,
    height INT,
    FOREIGN KEY (template_id) REFERENCES report_template(id)
);

-- 指标定义表
CREATE TABLE metric_definition (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    category VARCHAR(50),
    metric_type VARCHAR(20) NOT NULL,
    formula TEXT,
    dimensions TEXT,
    data_source VARCHAR(100),
    aggregation_type VARCHAR(20),
    unit VARCHAR(20),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 仪表板表
CREATE TABLE dashboard (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    layout TEXT,
    theme VARCHAR(50),
    refresh_interval VARCHAR(20),
    access_level VARCHAR(20) DEFAULT 'PRIVATE',
    owner_id VARCHAR(32),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 仪表板组件表
CREATE TABLE dashboard_widget (
    id VARCHAR(32) PRIMARY KEY,
    dashboard_id VARCHAR(32),
    widget_type VARCHAR(50) NOT NULL,
    title VARCHAR(100),
    data_source VARCHAR(100),
    query_config TEXT,
    chart_config TEXT,
    style_config TEXT,
    position_x INT,
    position_y INT,
    width INT,
    height INT,
    FOREIGN KEY (dashboard_id) REFERENCES dashboard(id)
);
```
```
