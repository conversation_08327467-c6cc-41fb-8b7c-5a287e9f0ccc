# FRS-03 M02数据治理模块功能规格说明书

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | FRS-03 |
| 文档名称 | M02数据治理模块功能规格说明书 |
| 版本号 | 1.0 |
| 创建日期 | 2025-06-27 |
| 最后修改日期 | 2025-06-27 |
| 文档状态 | 草稿 |

## 1. 模块架构

### 1.1 技术架构图

```mermaid
graph TB
    subgraph "治理服务层"
        A1[数据标准服务]
        A2[主数据服务]
        A3[数据质量服务]
        A4[元数据服务]
        A5[数据目录服务]
        A6[数据安全服务]
    end
    
    subgraph "规则引擎层"
        B1[标准规则引擎]
        B2[质量规则引擎]
        B3[安全规则引擎]
        B4[血缘分析引擎]
    end
    
    subgraph "数据处理层"
        C1[元数据采集器]
        C2[质量检查器]
        C3[血缘追踪器]
        C4[数据分类器]
    end
    
    subgraph "存储层"
        D1[标准库]
        D2[主数据库]
        D3[质量库]
        D4[元数据库]
        D5[目录库]
        D6[安全库]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B4
    A5 --> B4
    A6 --> B3
    
    B1 --> C4
    B2 --> C2
    B3 --> C4
    B4 --> C1
    B4 --> C3
    
    C1 --> D4
    C2 --> D3
    C3 --> D4
    C4 --> D1
    C4 --> D5
    
    A1 --> D1
    A2 --> D2
    A3 --> D3
    A4 --> D4
    A5 --> D5
    A6 --> D6
```

### 1.2 核心组件

#### 1.2.1 数据标准管理组件
- **标准定义器**：定义数据元素、分类编码等标准
- **标准验证器**：验证数据是否符合标准
- **标准监控器**：监控标准执行情况
- **标准版本管理器**：管理标准的版本变更

#### 1.2.2 主数据管理组件
- **主数据识别器**：识别和定义主数据实体
- **数据匹配器**：匹配和合并重复数据
- **数据分发器**：向各系统分发主数据
- **变更管理器**：管理主数据变更流程

#### 1.2.3 数据质量管理组件
- **质量规则引擎**：执行数据质量检查规则
- **质量监控器**：实时监控数据质量状况
- **质量分析器**：分析质量问题根因
- **质量改进器**：执行质量改进措施

## 2. 功能详细设计

### 2.1 数据标准管理

#### 2.1.1 数据标准实体设计
```java
@Entity
@Table(name = "data_standard")
public class DataStandard {
    @Id
    private String id;
    private String name;
    private String category;
    private String description;
    private String dataType;
    private String format;
    private String constraints;
    private String version;
    private String status;
    private Date createTime;
    private Date updateTime;
    
    // getters and setters
}

@Entity
@Table(name = "standard_category")
public class StandardCategory {
    @Id
    private String id;
    private String name;
    private String parentId;
    private String description;
    private Integer sortOrder;
    
    // getters and setters
}
```

#### 2.1.2 标准管理服务
```java
@Service
public class DataStandardService {
    
    @Autowired
    private DataStandardRepository standardRepository;
    
    @Autowired
    private StandardValidationEngine validationEngine;
    
    public DataStandard createStandard(DataStandardDTO dto) {
        // 创建数据标准
        DataStandard standard = new DataStandard();
        BeanUtils.copyProperties(dto, standard);
        standard.setId(UUID.randomUUID().toString());
        standard.setVersion("1.0");
        standard.setStatus("DRAFT");
        standard.setCreateTime(new Date());
        
        return standardRepository.save(standard);
    }
    
    public ValidationResult validateData(String standardId, Object data) {
        DataStandard standard = standardRepository.findById(standardId);
        return validationEngine.validate(data, standard);
    }
    
    public List<DataStandard> searchStandards(String keyword, String category) {
        return standardRepository.findByNameContainingAndCategory(keyword, category);
    }
}
```

### 2.2 主数据管理

#### 2.2.1 主数据实体设计
```java
@Entity
@Table(name = "master_data_entity")
public class MasterDataEntity {
    @Id
    private String id;
    private String name;
    private String description;
    private String entityType;
    private String schema; // JSON格式的实体结构
    private String status;
    private Date createTime;
    
    // getters and setters
}

@Entity
@Table(name = "master_data_record")
public class MasterDataRecord {
    @Id
    private String id;
    private String entityId;
    private String data; // JSON格式的数据记录
    private String sourceSystem;
    private String status;
    private String version;
    private Date createTime;
    private Date updateTime;
    
    // getters and setters
}
```

#### 2.2.2 主数据管理服务
```java
@Service
public class MasterDataService {
    
    @Autowired
    private MasterDataRepository masterDataRepository;
    
    @Autowired
    private DataMatchingEngine matchingEngine;
    
    @Autowired
    private DataMergingEngine mergingEngine;
    
    public MasterDataRecord createMasterData(String entityId, Map<String, Object> data, String sourceSystem) {
        // 创建主数据记录
        MasterDataRecord record = new MasterDataRecord();
        record.setId(UUID.randomUUID().toString());
        record.setEntityId(entityId);
        record.setData(JsonUtils.toJson(data));
        record.setSourceSystem(sourceSystem);
        record.setStatus("ACTIVE");
        record.setVersion("1.0");
        record.setCreateTime(new Date());
        
        return masterDataRepository.save(record);
    }
    
    public List<MasterDataRecord> findDuplicates(String entityId, Map<String, Object> data) {
        List<MasterDataRecord> allRecords = masterDataRepository.findByEntityId(entityId);
        return matchingEngine.findMatches(data, allRecords);
    }
    
    public MasterDataRecord mergeRecords(List<String> recordIds, String mergeStrategy) {
        List<MasterDataRecord> records = masterDataRepository.findAllById(recordIds);
        Map<String, Object> mergedData = mergingEngine.merge(records, mergeStrategy);
        
        // 创建合并后的记录
        MasterDataRecord mergedRecord = createMasterData(
            records.get(0).getEntityId(), 
            mergedData, 
            "SYSTEM_MERGE"
        );
        
        // 标记原记录为已合并
        records.forEach(record -> {
            record.setStatus("MERGED");
            masterDataRepository.save(record);
        });
        
        return mergedRecord;
    }
}
```

### 2.3 数据质量管理

#### 2.3.1 质量规则设计
```java
@Entity
@Table(name = "quality_rule")
public class QualityRule {
    @Id
    private String id;
    private String name;
    private String ruleType; // COMPLETENESS, ACCURACY, CONSISTENCY, VALIDITY, TIMELINESS
    private String description;
    private String ruleExpression; // 规则表达式
    private String targetTable;
    private String targetColumn;
    private String severity; // HIGH, MEDIUM, LOW
    private String status;
    private Date createTime;
    
    // getters and setters
}

@Entity
@Table(name = "quality_check_result")
public class QualityCheckResult {
    @Id
    private String id;
    private String ruleId;
    private String checkTime;
    private Integer totalRecords;
    private Integer passedRecords;
    private Integer failedRecords;
    private Double qualityScore;
    private String details; // JSON格式的详细结果
    
    // getters and setters
}
```

#### 2.3.2 质量检查引擎
```java
@Component
public class QualityCheckEngine {
    
    @Autowired
    private QualityRuleRepository ruleRepository;
    
    @Autowired
    private DataSourceService dataSourceService;
    
    public QualityCheckResult executeQualityCheck(String ruleId) {
        QualityRule rule = ruleRepository.findById(ruleId);
        
        // 获取目标数据
        List<Map<String, Object>> data = dataSourceService.queryData(
            rule.getTargetTable(), 
            rule.getTargetColumn()
        );
        
        // 执行质量检查
        QualityCheckResult result = new QualityCheckResult();
        result.setId(UUID.randomUUID().toString());
        result.setRuleId(ruleId);
        result.setCheckTime(new Date().toString());
        result.setTotalRecords(data.size());
        
        int passedCount = 0;
        List<Map<String, Object>> failedRecords = new ArrayList<>();
        
        for (Map<String, Object> record : data) {
            if (evaluateRule(record, rule.getRuleExpression())) {
                passedCount++;
            } else {
                failedRecords.add(record);
            }
        }
        
        result.setPassedRecords(passedCount);
        result.setFailedRecords(data.size() - passedCount);
        result.setQualityScore((double) passedCount / data.size() * 100);
        result.setDetails(JsonUtils.toJson(failedRecords));
        
        return result;
    }
    
    private boolean evaluateRule(Map<String, Object> record, String ruleExpression) {
        // 使用规则引擎评估规则表达式
        // 这里可以集成如Drools等规则引擎
        return RuleEvaluator.evaluate(ruleExpression, record);
    }
}
```

### 2.4 元数据管理

#### 2.4.1 元数据模型设计
```java
@Entity
@Table(name = "metadata_table")
public class MetadataTable {
    @Id
    private String id;
    private String tableName;
    private String schemaName;
    private String databaseName;
    private String description;
    private String tableType;
    private Integer recordCount;
    private String owner;
    private Date createTime;
    private Date updateTime;
    
    // getters and setters
}

@Entity
@Table(name = "metadata_column")
public class MetadataColumn {
    @Id
    private String id;
    private String tableId;
    private String columnName;
    private String dataType;
    private Integer length;
    private Boolean nullable;
    private String defaultValue;
    private String description;
    private String businessName;
    private Integer position;
    
    // getters and setters
}

@Entity
@Table(name = "data_lineage")
public class DataLineage {
    @Id
    private String id;
    private String sourceTableId;
    private String sourceColumnId;
    private String targetTableId;
    private String targetColumnId;
    private String transformRule;
    private String lineageType; // DIRECT, TRANSFORM, AGGREGATE
    
    // getters and setters
}
```

#### 2.4.2 血缘分析服务
```java
@Service
public class DataLineageService {
    
    @Autowired
    private DataLineageRepository lineageRepository;
    
    @Autowired
    private MetadataTableRepository tableRepository;
    
    public LineageGraph buildLineageGraph(String tableId, int depth) {
        LineageGraph graph = new LineageGraph();
        buildUpstreamLineage(tableId, depth, graph);
        buildDownstreamLineage(tableId, depth, graph);
        return graph;
    }
    
    private void buildUpstreamLineage(String tableId, int depth, LineageGraph graph) {
        if (depth <= 0) return;
        
        List<DataLineage> upstreamLineages = lineageRepository.findByTargetTableId(tableId);
        for (DataLineage lineage : upstreamLineages) {
            graph.addEdge(lineage.getSourceTableId(), lineage.getTargetTableId(), lineage);
            buildUpstreamLineage(lineage.getSourceTableId(), depth - 1, graph);
        }
    }
    
    private void buildDownstreamLineage(String tableId, int depth, LineageGraph graph) {
        if (depth <= 0) return;
        
        List<DataLineage> downstreamLineages = lineageRepository.findBySourceTableId(tableId);
        for (DataLineage lineage : downstreamLineages) {
            graph.addEdge(lineage.getSourceTableId(), lineage.getTargetTableId(), lineage);
            buildDownstreamLineage(lineage.getTargetTableId(), depth - 1, graph);
        }
    }
    
    public ImpactAnalysisResult analyzeImpact(String tableId, String changeType) {
        LineageGraph downstreamGraph = new LineageGraph();
        buildDownstreamLineage(tableId, 10, downstreamGraph);

        ImpactAnalysisResult result = new ImpactAnalysisResult();
        result.setSourceTable(tableId);
        result.setChangeType(changeType);
        result.setImpactedTables(downstreamGraph.getAllNodes());
        result.setImpactLevel(calculateImpactLevel(downstreamGraph));

        return result;
    }
}
```

### 2.5 数据资源目录

#### 2.5.1 数据目录实体设计
```java
@Entity
@Table(name = "data_catalog")
public class DataCatalog {
    @Id
    private String id;
    private String name;
    private String description;
    private String category;
    private String dataSource;
    private String dataType;
    private String format;
    private String owner;
    private String tags;
    private String accessLevel; // PUBLIC, INTERNAL, CONFIDENTIAL
    private String status;
    private Date createTime;
    private Date updateTime;

    // getters and setters
}

@Entity
@Table(name = "data_access_request")
public class DataAccessRequest {
    @Id
    private String id;
    private String catalogId;
    private String requesterId;
    private String purpose;
    private String accessType; // READ, WRITE, DOWNLOAD
    private String status; // PENDING, APPROVED, REJECTED
    private String approverComment;
    private Date requestTime;
    private Date approveTime;

    // getters and setters
}
```

#### 2.5.2 数据目录服务
```java
@Service
public class DataCatalogService {

    @Autowired
    private DataCatalogRepository catalogRepository;

    @Autowired
    private DataAccessRequestRepository accessRequestRepository;

    public List<DataCatalog> searchCatalog(String keyword, String category, String dataType) {
        return catalogRepository.findByKeywordAndCategoryAndDataType(keyword, category, dataType);
    }

    public DataAccessRequest requestAccess(String catalogId, String requesterId, String purpose, String accessType) {
        DataAccessRequest request = new DataAccessRequest();
        request.setId(UUID.randomUUID().toString());
        request.setCatalogId(catalogId);
        request.setRequesterId(requesterId);
        request.setPurpose(purpose);
        request.setAccessType(accessType);
        request.setStatus("PENDING");
        request.setRequestTime(new Date());

        return accessRequestRepository.save(request);
    }

    public DataAccessRequest approveAccess(String requestId, String approverId, String comment) {
        DataAccessRequest request = accessRequestRepository.findById(requestId);
        request.setStatus("APPROVED");
        request.setApproverComment(comment);
        request.setApproveTime(new Date());

        // 创建访问权限
        createAccessPermission(request);

        return accessRequestRepository.save(request);
    }
}
```

### 2.6 数据安全管理

#### 2.6.1 数据分级分类
```java
@Entity
@Table(name = "data_classification")
public class DataClassification {
    @Id
    private String id;
    private String name;
    private String level; // PUBLIC, INTERNAL, CONFIDENTIAL, SECRET
    private String description;
    private String protectionRules; // JSON格式的保护规则

    // getters and setters
}

@Entity
@Table(name = "sensitive_data_rule")
public class SensitiveDataRule {
    @Id
    private String id;
    private String ruleName;
    private String pattern; // 正则表达式或规则模式
    private String dataType; // ID_CARD, PHONE, EMAIL, BANK_CARD
    private String maskingRule; // 脱敏规则
    private String status;

    // getters and setters
}
```

#### 2.6.2 数据安全服务
```java
@Service
public class DataSecurityService {

    @Autowired
    private SensitiveDataRuleRepository ruleRepository;

    @Autowired
    private DataClassificationRepository classificationRepository;

    public Map<String, Object> maskSensitiveData(Map<String, Object> data) {
        Map<String, Object> maskedData = new HashMap<>(data);
        List<SensitiveDataRule> rules = ruleRepository.findByStatus("ACTIVE");

        for (SensitiveDataRule rule : rules) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String value = String.valueOf(entry.getValue());
                if (Pattern.matches(rule.getPattern(), value)) {
                    String maskedValue = applyMaskingRule(value, rule.getMaskingRule());
                    maskedData.put(entry.getKey(), maskedValue);
                }
            }
        }

        return maskedData;
    }

    private String applyMaskingRule(String value, String maskingRule) {
        // 根据脱敏规则对数据进行脱敏处理
        switch (maskingRule) {
            case "PHONE_MASK":
                return value.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
            case "ID_CARD_MASK":
                return value.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2");
            case "EMAIL_MASK":
                return value.replaceAll("(\\w{1,3})\\w*@", "$1***@");
            default:
                return "***";
        }
    }

    public boolean checkDataAccess(String userId, String dataId, String operation) {
        // 检查用户是否有权限访问指定数据
        // 实现基于角色和属性的访问控制
        return accessControlEngine.checkPermission(userId, dataId, operation);
    }
}
```

## 3. API接口设计

### 3.1 数据标准管理API

```yaml
paths:
  /api/v1/standards:
    get:
      summary: 获取数据标准列表
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: keyword
          in: query
          schema:
            type: string
      responses:
        200:
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/DataStandard'

    post:
      summary: 创建数据标准
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataStandardDTO'
      responses:
        201:
          description: 创建成功

  /api/v1/standards/{id}/validate:
    post:
      summary: 验证数据是否符合标准
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
      responses:
        200:
          description: 验证结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResult'
```

### 3.2 数据质量管理API

```yaml
paths:
  /api/v1/quality/rules:
    get:
      summary: 获取质量规则列表
      responses:
        200:
          description: 成功

    post:
      summary: 创建质量规则
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QualityRuleDTO'
      responses:
        201:
          description: 创建成功

  /api/v1/quality/checks/{ruleId}/execute:
    post:
      summary: 执行质量检查
      parameters:
        - name: ruleId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: 检查结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QualityCheckResult'
```
```
